import type { CognitiveSubject, defaultAffectiveSkills, defaultCognitiveSubjects, defaultPsychomotorSkills } from './store';

export type User = {
	u: string;
};

export type DBChatMessage = Pick<Message, 's' | 'u' | 'm' | 'd' | 'r'> | 'h';

export type SendChatMessage = Pick<Message, 'saved' | 'm' | 'i' | 'c' | 'd' | 't'>;

export type ChatMessage = Pick<Message, 'saved' | 'm' | 'i' | 'x'>;

export interface Message {
	saved?: boolean; // if client has received websocket event for this message, meaning message has been saved to db
	m: string; // message
	i: string; //id
	x?: string; // sender user tag,
	d: number; // date
	c: string; // cf id
	h: number; // has reply
	t: string; // receiver's (room/user) tag
	s?: 'm'; // tenant id for messages
	u?: string; // user ID
	r: string; // receiver ID (room/user)
	tc?: number; // token count
}

export interface UIReport {
	i?: string;
	s: string;
	n: string; // fullName
	c: string; // class
	d: string; // department
	e: string; // session
	g: string; // gender
	p: number | null; // daysPresent
	a: number | null; // daysAbsent
	o: number | null; // daysSchoolOpened
	t: number | null; // totalPupils
	v: number | null; // classAverage
	x: Record<keyof typeof defaultCognitiveSubjects, CognitiveSubject>; // cognitive
	y: Record<keyof typeof defaultPsychomotorSkills, string>; // psychomotor
	z: Record<keyof typeof defaultAffectiveSkills, string>; // affective
	r: string; // teacherRemark
	m: string; // resumptionDate
}

export type Year = '1' | '2' | '3' | '4' | '5';
export type Term = 1 | 2 | 3;
export type SchoolYear = number; // academic year e.g 2024 if 2024/2025 session

export type School = {
	i: string;
	n: string; // name
} & Record<SchoolYear, Record<Term, SchoolTerm>>;

export type SchoolTerm = {
	d: number; // days school opened
} & Record<Year, ClassTerm>; // primary years

export type Subject = {
	h: null | number; //highest scored
};

export type ClassTerm = {
	n: number; // number of students in class
	s: Record<keyof typeof defaultCognitiveSubjects, Subject>;
};

export type Class = Record<Term, ClassTerm>;
