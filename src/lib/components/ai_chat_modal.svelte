<script lang="ts">
	import { fade } from 'svelte/transition';
	import { tick, onMount, onDestroy } from 'svelte';
	import { toast } from '$lib/util/toast';
	import { browser } from '$app/environment';
	import axios from 'axios';
	import { marked } from 'marked';

	interface ChatMessage {
		role: 'user' | 'assistant';
		content: string;
		timestamp: number;
	}

	const {
		open,
		report_id,
		on_close,
		trigger_el
	} = $props<{
		open: boolean;
		report_id: string;
		on_close: () => void;
		trigger_el?: HTMLElement;
	}>();

	// Modal state
	let panel_el: HTMLDivElement | null = null;
	let close_btn_el: HTMLButtonElement | null = null;
	let start_sentinel_el: HTMLSpanElement | null = null;
	let end_sentinel_el: HTMLSpanElement | null = null;
	let message_input_el: HTMLTextAreaElement | null = null;
	let messages_container_el: HTMLDivElement | null = null;

	// Chat state
	let messages: ChatMessage[] = $state([]);
	let current_message = $state('');
	let is_loading = $state(false);

	// Focus management
	const focus_first = () => {
		if (message_input_el) {
			message_input_el.focus();
		} else if (close_btn_el) {
			close_btn_el.focus();
		}
	};

	const focus_last = () => {
		if (close_btn_el) {
			close_btn_el.focus();
		}
	};

	const close = () => {
		on_close();
		// Return focus to trigger element
		if (trigger_el && browser) {
			tick().then(() => trigger_el.focus());
		}
	};

	const handle_keydown = (e: KeyboardEvent) => {
		if (e.key === 'Escape') {
			e.preventDefault();
			close();
		}
	};

	const scroll_to_bottom = () => {
		if (messages_container_el) {
			tick().then(() => {
				if (messages_container_el) {
					messages_container_el.scrollTop = messages_container_el.scrollHeight;
				}
			});
		}
	};

	const send_message = async () => {
		if (!current_message.trim() || is_loading) return;

		const user_message = current_message.trim();
		current_message = '';

		// Add user message to chat
		const user_chat_message: ChatMessage = {
			role: 'user',
			content: user_message,
			timestamp: Date.now()
		};
		messages = [...messages, user_chat_message];
		scroll_to_bottom();

		is_loading = true;

		try {
			// Send message to backend
			const response = await axios.post(`/${report_id}/c`, {
				m: user_message,
				d: Date.now(),
				i: crypto.randomUUID(),
				c: report_id,
				t: report_id
			});

			// Add AI response to chat
			const ai_message: ChatMessage = {
				role: 'assistant',
				content: response.data,
				timestamp: Date.now()
			};
			messages = [...messages, ai_message];
			scroll_to_bottom();
		} catch (error) {
			console.error('Error sending message:', error);
			toast('Failed to send message. Please try again.');
		} finally {
			is_loading = false;
		}
	};

	const handle_input_keydown = (e: KeyboardEvent) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			send_message();
		}
	};

	// Initialize with AI analysis when modal opens
	$effect(() => {
		if (open && messages.length === 0) {
			// Start with loading state
			is_loading = true;

			// Request initial analysis from AI
			axios.post(`/${report_id}/c`, {
				m: 'Please provide an initial analysis of this report card.',
				d: Date.now(),
				i: crypto.randomUUID(),
				c: report_id,
				t: report_id
			}).then(response => {
				messages = [{
					role: 'assistant',
					content: response.data,
					timestamp: Date.now()
				}];
				scroll_to_bottom();
			}).catch(error => {
				console.error('Error getting initial analysis:', error);
				messages = [{
					role: 'assistant',
					content: 'Hello! I can help you analyse this report card. Ask me about the student\'s performance, grades, or any insights you\'d like.',
					timestamp: Date.now()
				}];
			}).finally(() => {
				is_loading = false;
			});
		}
	});

	// Focus management when modal opens
	$effect(() => {
		if (open && browser) {
			tick().then(() => {
				focus_first();
			});
		}
	});

	// Scroll to bottom when messages change
	$effect(() => {
		if (messages.length > 0) {
			scroll_to_bottom();
		}
	});
</script>

{#if open}
	<div class="u-modal_backdrop" role="presentation" transition:fade onclick={close} />
	<div class="u-modal_wrap" aria-hidden="false" transition:fade>
		<!-- start focus sentinel -->
		<span
			class="u-sr_only"
			tabindex="0"
			aria-hidden="true"
			bind:this={start_sentinel_el}
			onfocus={focus_last}
		></span>
		<div
			class="u-modal_panel chat-modal"
			bind:this={panel_el}
			role="dialog"
			aria-modal="true"
			aria-labelledby="chat-title"
			aria-describedby="chat-desc"
			onkeydown={handle_keydown}
		>
			<p id="chat-desc" class="u-sr_only">
				AI chat for report analysis. Type your message and press Enter to send. Press Escape to close.
			</p>
			<div class="u-modal_header">
				<h2 id="chat-title" class="u-modal_title heading-2">Analyse Report</h2>
				<button
					class="u-modal_close"
					aria-label="Close dialog"
					bind:this={close_btn_el}
					onclick={close}
				>
					<span class="u-sr_only">Close</span>
					<span aria-hidden="true">×</span>
				</button>
			</div>
			<div class="u-modal_body chat-body">
				<div class="chat-messages" bind:this={messages_container_el}>
					{#each messages as message (message.timestamp)}
						<div class="chat-message {message.role}">
							<div class="message-content">
								{@html marked(message.content)}
							</div>
							<div class="message-time">
								{new Date(message.timestamp).toLocaleTimeString()}
							</div>
						</div>
					{/each}
					{#if is_loading}
						<div class="chat-message assistant loading">
							<div class="message-content">
								<span class="loading-dots">Thinking...</span>
							</div>
						</div>
					{/if}
				</div>
				<div class="chat-input-area">
					<textarea
						bind:this={message_input_el}
						bind:value={current_message}
						placeholder="Ask about the report..."
						class="chat-input"
						rows="2"
						disabled={is_loading}
						onkeydown={handle_input_keydown}
					></textarea>
					<button
						class="btn btn-primary send-btn"
						disabled={!current_message.trim() || is_loading}
						onclick={send_message}
					>
						Send
					</button>
				</div>
			</div>
		</div>
		<!-- end focus sentinel -->
		<span
			class="u-sr_only"
			tabindex="0"
			aria-hidden="true"
			bind:this={end_sentinel_el}
			onfocus={focus_first}
		></span>
	</div>
{/if}

<style>
	.chat-modal {
		max-width: clamp(20rem, 92vw, 50rem);
		height: clamp(30rem, 80vh, 40rem);
		display: flex;
		flex-direction: column;
	}

	.chat-body {
		display: flex;
		flex-direction: column;
		height: 100%;
		padding: 0;
	}

	.chat-messages {
		flex: 1;
		overflow-y: auto;
		padding: 1rem;
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.chat-message {
		display: flex;
		flex-direction: column;
		max-width: 80%;
	}

	.chat-message.user {
		align-self: flex-end;
	}

	.chat-message.user .message-content {
		background: var(--brand-1);
		color: #fff;
		padding: 0.75rem 1rem;
		text-align: right;
	}

	.chat-message.assistant .message-content {
		background: var(--surface-muted);
		color: var(--ink);
		padding: 0.75rem 1rem;
		border: 1px solid var(--line-soft);
	}

	.message-time {
		font-size: 0.75rem;
		color: var(--ink-muted);
		margin-top: 0.25rem;
		text-align: inherit;
	}

	.chat-message.user .message-time {
		text-align: right;
	}

	.chat-input-area {
		display: flex;
		gap: 0.5rem;
		padding: 1rem;
		border-top: 1px solid var(--line-soft);
		background: var(--surface);
	}

	.chat-input {
		flex: 1;
		resize: none;
		border: 1px solid var(--line-soft);
		padding: 0.5rem;
		background: #fff;
		color: var(--ink);
		font-family: inherit;
	}

	.chat-input:focus {
		outline: 2px solid var(--focus);
		outline-offset: 1px;
	}

	.send-btn {
		align-self: flex-end;
	}

	.loading-dots {
		animation: pulse 1.5s ease-in-out infinite;
	}

	@keyframes pulse {
		0%, 100% { opacity: 1; }
		50% { opacity: 0.5; }
	}

	:global(.message-content p:first-child) {
		margin-top: 0;
	}

	:global(.message-content p:last-child) {
		margin-bottom: 0;
	}
</style>
