<script lang="ts">
	import { browser } from '$app/environment';
	import { cognitive_map, type Report, type Totals } from '$lib/store.js';
	import { grade_remark } from '$lib/util/grade_remark';
	import ScoreCell from './ScoreCell.svelte';
	let {
		report = $bindable(),
		totals = $bindable(),
		view_mode
	}: {
		totals: Totals;
		report: Report;
		view_mode: boolean;
	} = $props();
</script>

<table class="table-compact table">
	<thead class="thead">
		<tr class="caps">
			<th rowspan="2" class="th cognitive-header fw-bold">COGNITIVE DOMAIN</th>
			<!-- Show CA breakdown on desktop and print -->
			<th class="th cognitive-header desktop-only fw-bold" rowspan="2">PROJECT 10%</th>
			<th class="th cognitive-header desktop-only fw-bold" rowspan="2">1st C.A 5%</th>
			<th class="th cognitive-header desktop-only fw-bold" rowspan="2">2nd C.A 20%</th>
			<th class="th cognitive-header desktop-only fw-bold" rowspan="2">3rd C.A 5%</th>
			<!-- Keep compact C.A/EXAM columns for mobile -->
			<th class="th cognitive-header fw-bold">C.A. TOTAL 40%</th>
			<th class="th cognitive-header fw-bold">EXAM SCORES 60%</th>
			<th class="th cognitive-header fw-bold" rowspan="2">TOTAL</th>
			<th class="th cognitive-header fw-bold" rowspan="2">HIGHEST SCORED</th>
			<th class="th cognitive-header fw-bold" rowspan="2">GRADE</th>
			<th class="th cognitive-header fw-bold" rowspan="2">REMARK</th>
		</tr>
	</thead>
	<tbody>
		{#each Object.entries(report.x) as [s, v] (s)}
			{@const caTotal = v[1] + v[2] + v[3] + v.p}
			{@const totalScore = caTotal + v.e}
			{@const [grade, remark] = grade_remark(totalScore)}
			<tr>
				<td class="td td-left fw-bold caps">{cognitive_map[s]}</td>
				<!-- Show CA breakdown on desktop and print -->
				<ScoreCell desktop_only bind:report {s} {view_mode} a="p" />
				<ScoreCell desktop_only bind:report {s} {view_mode} a={1} />
				<ScoreCell desktop_only bind:report {s} {view_mode} a={2} />
				<ScoreCell desktop_only bind:report {s} {view_mode} a={3} />
				<td class="td td-center">{totals.x[s].c}</td>
				<ScoreCell bind:report {s} {view_mode} a="e" />
				<td class="td fw-bold td-center">{totals.x[s].t}</td>
				<td class="td td-center">
					{#if !view_mode}
						<input
							type="number"
							min="0"
							max="60"
							class="input ta-center"
						/>
					{:else}
						<span class="ta-center">{report.x[s].h}</span>
					{/if}
				</td>
				<td class="td fw-bold td-center">{totals.x[s].g}</td>
				<td class="td td-center fw-bold">{totals.x[s].r}</td>
			</tr>
		{/each}
	</tbody>
</table>

<style>
	.cognitive-header {
		white-space: pre-line;
		text-align: center;
		line-height: 1.2;
		width: 100%;
	}
</style>
