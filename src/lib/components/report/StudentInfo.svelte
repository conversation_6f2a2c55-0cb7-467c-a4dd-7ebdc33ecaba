<script lang="ts">
	import { browser } from '$app/environment';
	import type { Report } from '$lib/store';

	const { report = $bindable(), view_mode } = $props<{
		report: Report;
		view_mode: boolean;
	}>();
	function displayValue(value: any, placeholder = '—') {
		return value !== null && value !== undefined && value !== '' ? value : placeholder;
	}

	let innerWidth: number = $state(0);
</script>

<svelte:window bind:innerWidth />

<div class="grid-1-2-s gap-sm mb-4 border-t-4 px-4 py-3">
	<div class="t-sm">
		<div class="mb-2">
			<span class="fw-bold inline-block shrink-0">FULL NAME:</span>
			{#if !view_mode}
				<input type="text" class="input" bind:value={report.n} />
			{:else}
				<span>&nbsp;{displayValue(report.n)}</span>
			{/if}
		</div>
		<div class="mb-2">
			<span class="fw-bold inline-block shrink-0">CLASS:</span>
			{#if !view_mode}
				<select class="input" bind:value={report.c}>
					<option value={1}>Year 1</option>
					<option value={2}>Year 2</option>
					<option value={3}>Year 3</option>
					<option value={4}>Year 4</option>
					<option value={5}>Year 5</option>
				</select>
			{:else}
				<span>&nbsp;{displayValue(report.c)}</span>
			{/if}
		</div>
		<div class="mb-2">
			<span class="fw-bold inline-block shrink-0">SESSION:</span>
			{#if !view_mode}
				<input type="text" class="input" bind:value={report.e} />
			{:else}
				<span>&nbsp;{displayValue(report.e)}</span>
			{/if}
		</div>
		<div>
			<span class="fw-bold inline-block shrink-0">GENDER:</span>
			{#if !view_mode}
				<select class="input" bind:value={report.g}>
					<option value={0}>Male</option>
					<option value={1}>Female</option>
				</select>
			{:else}
				<span>&nbsp;{report.g === 0 ? 'MALE' : 'FEMALE'}</span>
			{/if}
		</div>
	</div>
	<div class="t-sm">
		<div class="mb-2">
			<span class="fw-bold inline-block shrink-0">DAYS SCHOOL OPENED:</span>
			{#if !view_mode}
				<input type="number" class="input" bind:value={report.o} />
			{:else}
				<span>&nbsp;{displayValue(report.o)}</span>
			{/if}
		</div>
		<div class="mb-2">
			<span class="fw-bold inline-block shrink-0">DAYS PRESENT:</span>
			{#if !view_mode}
				<input type="number" class="input" bind:value={report.p} />
			{:else}
				<span>&nbsp;{displayValue(report.p)}</span>
			{/if}
		</div>
		<div class="mb-2">
			<span class="fw-bold inline-block shrink-0">DAYS ABSENT:</span>
			{#if !view_mode}
				<input type="number" class="input" bind:value={report.a} />
			{:else}
				<span>&nbsp;{displayValue(report.a)}</span>
			{/if}
		</div>
		<div>
			<span class="fw-bold inline-block shrink-0">NUMBER IN CLASS:</span>
			<span>&nbsp;{displayValue(report.q)}</span>
		</div>
	</div>
</div>
