// src/lib/store.js
import { writable } from 'svelte/store';
import { browser } from '$app/environment';
import { grade_remark } from './util/grade_remark';


export interface Totals {
	x: Record<string, { c: number; t: number, g: string; r: string }>;
	t: number;
	a: number;
	g: string;
	r: string;
}

export const calculate_totals = (report: Report): Totals => {
	let total = 0;

	const results: Totals = {
		x: {},
		t: 0,
		a: 0,
		g: '',
		r: ''
	};

	for (const [s, v] of Object.entries(report.x) || []) {
		const ca = v.p + v[1] + v[2] + v[3];
		const subjectTotal = ca + v.e;
		results.x[s] = { c: ca, t: subjectTotal };
		[results.x[s].g, results.x[s].r] = grade_remark(subjectTotal);
		total += subjectTotal;
	}

	results.t = total;
	results.a = total / Object.keys(report.x).length;
	const [grade, remark] = grade_remark(results.a);
	results.g = grade;
	results.r = remark;

	return results;
};
export interface Report {
	i?: string;
	s: string;
	n: string; // fullName
	c: number | null; // class
	d: string; // department
	e: string; // session
	t: 1 | 2 | 3; // term
	_: number; // academic year e.g 2024 if 2024/2025 session
	g: number; // gender - 0 male, 1 female
	p: number | null; // daysPresent
	a: number | null; // daysAbsent
	o: number | null; // daysSchoolOpened
	q: number | null; // totalPupils
	// v: number | null; // classAverage
	x: Record<keyof typeof defaultCognitiveSubjects, CognitiveSubject>; // cognitive
	y: Record<keyof typeof defaultPsychomotorSkills, string>; // psychomotor
	z: Record<keyof typeof defaultAffectiveSkills, string>; // affective
	r: string; // teacherRemark
	// m: string; // resumptionDate
}

export interface CognitiveSubject {
	p: number;
	1: number;
	2: number;
	3: number;
	e: number;
}

export const defaultPsychomotorSkills: Record<string, string> = {
	f: 'A',
	h: 'A',
	s: 'A',
	c: 'A',
	p: 'A',
	a: 'A',
	o: 'A'
};

export const defaultAffectiveSkills: Record<string, string> = {
	a: 'A',
	s: 'A',
	m: 'A',
	c: 'A',
	t: 'A',
	p: 'A',
	i: 'A'
};

export const defaultCognitiveSubjects = {
	e: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	m: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	bst: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	phe: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	nv: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	pvs: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	h: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	c: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	rs: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	cca: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	f: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 },
	ms: { p: 0, 1: 0, 2: 0, 3: 0, e: 0 }
};

export const default_report: Report = {
	n: '',
	s: 'sr',
	c: null,
	d: 'PRIMARY',
	e: '2024/2025',
	g: 0,
	p: null,
	a: null,
	o: null,
	t: null,
	v: null,
	x: JSON.parse(JSON.stringify(defaultCognitiveSubjects)),
	y: { ...defaultPsychomotorSkills },
	z: { ...defaultAffectiveSkills },
	r: '',
	m: 'January 8th, 2025'
};

export const cognitive_map = {
	e: 'ENGLISH STUDIES',
	m: 'MATHEMATICS',
	bst: 'BASIC SCIENCE & TECH.',
	phe: 'PHYSICAL & HEALTH EDU.',
	nv: 'NATIONAL VALUES',
	f: 'FRENCH',
	pvs: 'PRE-VOCATIONAL STUDIES',
	h: 'HISTORY',
	ms: 'MUSIC',
	c: 'COMPUTER STUDIES',
	rs: 'CHRISTIAN REL. STUDIES',
	cca: 'CUL. AND CREATIVE ART'
};

export const psychomotorSkillMap = {
	f: 'FLUENCY',
	h: 'HANDWRITING',
	s: 'SPORTS & GAMES',
	c: 'CREATIVITY',
	p: 'PUNCTUALITY',
	a: 'ATTENDANCE',
	o: 'ORDERLINESS'
};

export const affectiveSkillMap = {
	a: 'ADAPTABILITY',
	s: 'SELF-CONTROL',
	m: 'AMIABILITY',
	c: 'ACCOUNTABILITY',
	t: 'ATTENTIVENESS',
	p: 'PERSEVERANCE',
	i: 'INITIATIVE'
};

// --- Store Logic ---
const initialStudentData = [default_report];

// Function to load from localStorage
const loadStudents = (): Report[] => {
	if (!browser) return initialStudentData;
	const stored = localStorage.getItem('studentReports');
	try {
		const parsed: Report[] = stored ? JSON.parse(stored) : initialStudentData;
		return parsed.map((student: Partial<Report>) => ({
			...default_report,
			...student,
			x: { ...defaultCognitiveSubjects, ...(student.x || {}) },
			y: { ...defaultPsychomotorSkills, ...(student.y || {}) },
			z: { ...defaultAffectiveSkills, ...(student.z || {}) }
		}));
	} catch (e) {
		console.error('Error parsing student data from localStorage:', e);
		localStorage.removeItem('studentReports');
		return initialStudentData;
	}
};

// Create the writable store
const { subscribe, set, update } = writable(loadStudents());

// Subscribe to changes and save to localStorage
if (browser) {
	subscribe((value) => {
		try {
			// Add a check for empty array before stringifying
			if (value && value.length > 0) {
				localStorage.setItem('studentReports', JSON.stringify(value));
			} else {
				localStorage.removeItem('studentReports'); // Clear storage if array is empty
			}
		} catch (e) {
			console.error('Error saving student data to localStorage:', e);
		}
	});
}

// Export store functions and helpers
export const studentReports = {
	subscribe,
	set,
	update,
	addStudent: () => update((students: Report[]) => [...students, createNewStudent()]),
	removeStudent: (id: string) =>
		update((students: Report[]) => students.filter((s: Report) => s.id !== id))
};

// Export only needed helpers
export { calculateGrade, getOverallRemark };
