<script lang="ts">
	import { onMount } from 'svelte';
	import type { PageData } from './$types';
	import axios from 'axios';
	import { cognitive_map } from '$lib/store';
	import type { School } from '$lib/types';

	const { data } = $props<{ data: PageData }>();
	let view_mode = $state(true);
	let school: School = $state(data.s);
	let saving = $state(false);
	let saveTimeout: ReturnType<typeof setTimeout>;
	let showSubjectModal = $state(false);
	let school_year = 2024

	let term = $state(3);
	let year = $state(4);

	let availableSubjects = $derived.by(() => {
		const currentSubjects = Object.keys(school?.d?.[year]?.[term] || {});
		return Object.entries(cognitive_map)
			.filter(([key]) => !currentSubjects.includes(key))
			.map(([key, value]) => ({ key, value }));
	});

	function addSubject(subjectKey: string) {
		if (!school.d) school.d = {};
		if (!school.d[year]) school.d[year] = {};
		if (!school.d[year][term]) school.d[year][term] = {};

		school.d[year][term][subjectKey] = { h: null };
		showSubjectModal = false;
	}

	$effect(() => {
		JSON.stringify(school) // so effect runs when nested values of `school` change
		clearTimeout(saveTimeout);
		saveTimeout = setTimeout(async () => {
			try {
				saving = true;
				await axios.put(`/sc/${school.i}`, school);
				console.log('saved')
			} catch (error) {
				console.error('Failed to save changes:', error);
			} finally {
				saving = false;
			}
		}, 1440);
	});

	onMount(() => {
		return () => {
			clearTimeout(saveTimeout);
		};
	});
</script>

<div class="container mx-auto p-4">
	<div class="mb-4 flex items-center justify-between">
		<h1 class="text-2xl font-bold">School Configuration</h1>
		<div class="flex items-center gap-2">
			{#if saving}
				<span class="text-sm text-gray-500">Saving...</span>
			{/if}
			<button
				class="btn"
				onclick={() => {
					view_mode = !view_mode;
				}}
			>
				{view_mode ? 'Edit' : 'View'}
			</button>
		</div>
	</div>

	<div class="card p-4">
		<div class="mb-4">
			<label class="label" for="school-name">School Name</label>
			{#if view_mode}
				<div id="school-name" class="input">{school.n}</div>
			{:else}
				<input type="text" class="input" bind:value={school.n} />
			{/if}
		</div>

		<div class="mb-4 flex gap-4">
			<div class="flex-1">
				<label for="class" class="label">Class</label>
				{#if view_mode}
					<div class="input">Year {year}</div>
				{:else}
					<select id="class" class="input" bind:value={year}>
						{#each [1,2,3,4,5] as cls (cls)}
							<option value={cls}>Year {cls}</option>
						{/each}
					</select>
				{/if}
			</div>

			<div class="flex-1">
				<label for="term" class="label">Term</label>
				{#if view_mode}
					<div class="input">Term {term}</div>
				{:else}
					<select id="term" class="input" bind:value={term}>
						{#each [1,2,3] as t (t)}
							<option value={t}>Term {t}</option>
						{/each}
					</select>
				{/if}
			</div>
		</div>

		<div class="mb-4">
			<label class="label" for="num-students">Number of Students</label>
			{#if view_mode}
				<div class="input">{school[school_year][term][year]?.n || 0}</div>
			{:else}
				<input 
					type="number" 
					id="num-students"
					class="input" 
					min="0"
					bind:value={school[school_year][term][year].n}
				/>
			{/if}
		</div>

		<div class="mb-4">
			<label class="label" for="term-duration">Term Duration (days)</label>
			{#if view_mode}
				<div class="input">{school[school_year][term]?.d || 0}</div>
			{:else}
				<input 
					type="number" 
					id="term-duration"
					class="input" 
					min="0"
					bind:value={school[school_year][term].d}
				/>
			{/if}
		</div>

		<div class="mb-4">
			<div class="mb-2 flex items-center justify-between">
				<h2 class="text-xl font-bold">Subjects</h2>
				{#if !view_mode}
					<button class="btn btn-sm" onclick={() => (showSubjectModal = true)}>
						Add Subject
					</button>
				{/if}
			</div>
			{#if school[school_year][term][year]}
				{#each Object.entries(school[school_year][term][year].s) as [s, v] (s)}
					<div class="mb-2 flex gap-4">
						<div class="flex-grow">
							<div class="input" id="subject-name">{cognitive_map[s]}</div>
						</div>
						<div>
							<label class="label" for="highest-score">Highest Score</label>
							{#if view_mode}
								<div class="input" id="highest-score">{v.h}</div>
							{:else}
								<input
									id="highest-score"
									type="number"
									class="input"
									min="0"
									max="100"
									bind:value={school[school_year][term][year].s[s].h}
								/>
							{/if}
						</div>
					</div>
				{/each}
			{/if}
		</div>
	</div>
</div>

{#if showSubjectModal}
	<div 
		class="modal-backdrop"
		role="dialog"
		aria-modal="true"
		onclick={() => (showSubjectModal = false)}
		onkeydown={(e) => e.key === 'Escape' && (showSubjectModal = false)}
	>
		<div 
			class="modal"
			role="document"
			onclick={(e) => e.stopPropagation()}
			onkeydown={(e) => e.stopPropagation()}
		>
			<h3 class="modal-title">Add Subject</h3>
			<div class="modal-content">
				{#each availableSubjects as subject (subject.value)}
					<button 
						type="button"
						class="btn btn-block mb-2"
						onclick={() => addSubject(subject.key)}
					>
						{subject.value}
					</button>
				{/each}
			</div>
			<div class="modal-footer">
				<button 
					type="button"
					class="btn"
					onclick={() => (showSubjectModal = false)}
				>
					Close
				</button>
			</div>
		</div>
	</div>
{/if}

<style>
	.modal-backdrop {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}

	.modal {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		max-width: 500px;
		width: 90%;
	}

	.modal-title {
		font-size: 1.5rem;
		font-weight: bold;
		margin-bottom: 1rem;
	}

	.modal-content {
		max-height: 60vh;
		overflow-y: auto;
	}

	.modal-footer {
		margin-top: 1rem;
		display: flex;
		justify-content: flex-end;
	}
</style>
