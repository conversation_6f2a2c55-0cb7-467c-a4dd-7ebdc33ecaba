import { create, search_by_payload, get } from '$lib/db';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import type { Message } from '$lib/types';
import type { Report } from '$lib/store';
import { token_count } from '$lib/util/token_count';
import { GEMINI } from '$env/static/private';
import { GoogleGenAI } from '@google/genai/node';
import { error, text } from '@sveltejs/kit';
import { make_report_human_readable } from '$lib/util/make_report_human_readable';

interface ChatRequest {
	m: string; // message
	d: number; // date
	i: string; // id
	c: string; // cf id
	t: string; // receiver tag
}

export const POST: RequestHandler = async ({ request, params }) => {
	try {
		const { m: user_message }: ChatRequest = await request.json();

		if (!user_message || typeof user_message !== 'string') {
			error(400, 'Message is required');
		}

		const report_id = params.i;
		if (!report_id) {
			error(400, 'Report ID is required');
		}

		// Get the report data for context
		const report = (await get(report_id, true)) as Report;
		if (!report) {
			error(404, 'Report not found');
		}

		// Get chat history within token limit (720,000 tokens)
		const all_chat_messages = await search_by_payload<Message>(
			{ s: 'm', r: report_id },
			['m', 'd', 'tc'],
			200, // limit to recent messages
			{ key: 'd', direction: 'desc' }
		);

		// Get the latest message's token count to establish baseline
		const latest_message = all_chat_messages[0];
		const baseline_tc = latest_message?.tc || 0;
		const TOKEN_WINDOW = 720000;

		// Filter messages within the token window
		const chat_messages = all_chat_messages.filter((msg) => {
			const msg_tc = msg.tc || 0;
			return msg_tc > baseline_tc - TOKEN_WINDOW;
		});

		// Calculate total tokens and filter messages within limit
		let total_tokens = 0;
		const filtered_messages: Message[] = [];

		// Add current message token count
		const current_tokens = await token_count(user_message);
		total_tokens += current_tokens;

		// Add messages in reverse order (newest first) until we hit token limit
		for (const msg of chat_messages) {
			const msg_tokens = msg.tc || (await token_count(msg.m));
			if (total_tokens + msg_tokens > TOKEN_WINDOW) {
				break;
			}
			total_tokens += msg_tokens;
			filtered_messages.unshift(msg); // Add to beginning to maintain chronological order
		}

		// Initialize Gemini
		const ai = new GoogleGenAI({ vertexai: false, apiKey: GEMINI });

		// Create the prompt with context and conversation history
		const system_prompt = `You are an AI assistant helping to analyze a student's report card. You have access to the complete report data and should provide insightful, constructive analysis.

${JSON.stringify(make_report_human_readable(report))}

Please give very brief, concise and helpful analysis, insights, and suggestions based on the report data. Be encouraging while being honest about areas for improvement. Keep your response short and concise and formal and brief and straight to the point`;

		// Build conversation context
		const conversation_context =
			filtered_messages.length > 0 ? `\n\nPrevious conversation:\n${filtered_messages.map((msg) => `User: ${msg.m}`).join('\n')}\n\n` : '\n\n';

		// Check if this is an initial analysis request
		const is_initial_analysis =
			user_message.toLowerCase().includes('initial analysis') || user_message.toLowerCase().includes('analyze this report');

		let full_prompt;
		if (is_initial_analysis) {
			full_prompt = `${system_prompt}${conversation_context}Please provide a comprehensive initial analysis of this student's report card. Focus on:
1. Overall academic performance and trends
2. Strengths and areas for improvement
3. Attendance and behavior patterns
4. Specific recommendations for the student
5. Any notable achievements or concerns

User: ${user_message}\n\nAssistant:`;
		} else {
			full_prompt = `${system_prompt}${conversation_context}User: ${user_message}\n\nAssistant:`;
		}

		// Generate response
		const result = await ai.models.generateContent({
			model: 'gemini-2.0-flash-exp',
			contents: full_prompt
		});

		const ai_response = result.text || 'I apologize, but I was unable to generate a response. Please try again.';

		// Save user message to database
		const user_message_data = {
			s: 'm',
			d: Date.now(),
			m: user_message,
			r: report_id,
			tc: current_tokens
		};

		await create(user_message_data, user_message);

		// Save AI response to database
		const ai_tokens = await token_count(ai_response);
		const ai_message_data = {
			s: 'm',
			d: Date.now(),
			m: ai_response,
			r: report_id,
			tc: ai_tokens
		};

		await create(ai_message_data, ai_response);

		// Return only the AI response text
		return text(ai_response);
	} catch (err) {
		console.error('Chat error:', err);
		error(500, 'Failed to process chat message');
	}
};
