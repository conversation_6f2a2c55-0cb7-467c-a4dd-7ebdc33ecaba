import { get } from '$lib/db';
import type { Report } from '$lib/store';
import type { ClassTerm } from '$lib/types';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
	const r: Report = await get(params.i, true);
	const school = await get(r.sc, [`2024.${r.t}`]);
	const t: SchoolTerm = school[r._][r.t];
	const c: ClassTerm = [r.c];
	if (r && c?.s && r.x) {
		for (const field in r.x) {
			if (c.s[field] && c.s[field].h) {
				r.x[field].h = c.s[field].h;
			}
		}
	}
	r.o = t.d;
	r.q = c.n;
	return { r };
};
