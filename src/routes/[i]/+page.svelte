<script lang="ts">
	import Report from "$lib/components/Report.svelte";
	import type { PageProps } from "./$types";
	import type { Report as ReportType } from '$lib/store';
	import { PUBLIC_PASSWORD } from '$env/static/public';
	import axios from 'axios';
	import { goto } from '$app/navigation';

	const {data}: PageProps = $props()
	let creating = $state(false);

	console.log(data.r)

	const create_report = async () => {
		creating = true;
		try {
			const i = (await axios.post('/')).data;
			goto('/' + i + '?a=' + PUBLIC_PASSWORD);
		} finally {
			creating = false;
		}
	};

	const go_home = () => {
		goto('/?a=' + PUBLIC_PASSWORD);
	};
</script>

{#if data.a}
	<div class="container mb-4 flex items-center gap-2">
		<button onclick={go_home} class="btn btn-primary t-15px">Home</button>
		<button onclick={create_report} class="btn btn-primary t-15px" disabled={creating}>
			{#if creating}
				<span class="loading loading-spinner"></span>
				Creating...
			{:else}
				Create Report
			{/if}
		</button>
	</div>
{/if}

<Report report={data.r as ReportType} admin={!!data.a} />