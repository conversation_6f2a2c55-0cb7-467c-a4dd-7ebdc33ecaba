Authentication Plan:

1. Protected Routes:
   - Homepage (/)
   - School routes (/sc/*)
   - Login page at /144

2. Server-side Tasks:
   - For each request:
     * Check if URL is protected
     * If protected and user not authenticated, show blank page
     * If authenticated or not protected, show normal page
   
   - Login endpoint (/144):
     * GET: Display password form
     * POST: 
       - Read password from form
       - Check against bcrypt hash
       - If correct, mark user as authenticated
       - Send user to requested page or homepage

3. Client-side Tasks:
   - Show password input form
   - Send password to /144 when submitted

